/**
 * Invoices Repository - Compatibility Wrapper
 * 
 * This file provides backward compatibility for the deprecated repository pattern.
 * It wraps the new unified invoice storage system to maintain API compatibility.
 * 
 * @deprecated Use invoice storage utilities directly for new code
 */

import { getAllInvoices, saveInvoice, getInvoiceByNumber as getInvoiceByNumberFromStorage } from '@/lib/invoice-storage';

// Legacy invoice interface for backward compatibility
export interface InvoiceRecord {
  id?: number;
  invoiceNumber: string;
  freelancerId: number;
  commissionerId: number;
  projectId?: number;
  projectTitle?: string;
  issueDate: string;
  dueDate: string;
  totalAmount: number;
  status: 'draft' | 'sent' | 'paid' | 'on_hold' | 'cancelled' | 'overdue';
  milestones?: Array<{
    title: string;
    description?: string;
    rate: number;
    taskId?: number;
  }>;
  isCustomProject?: boolean;
  invoiceType?: string;
  invoicingMethod?: string;
  isAutoGenerated?: boolean;
  paidDate?: string;
  paidAmount?: number;
  paymentDetails?: {
    paymentId?: string;
    paymentMethod?: string;
    platformFee?: number;
    freelancerAmount?: number;
    currency?: string;
    processedAt?: string;
  };
  createdAt: string;
  updatedAt: string;
}

/**
 * Get all invoices
 * @deprecated Use getAllInvoices from invoice-storage directly
 */
export async function readAllInvoices(): Promise<InvoiceRecord[]> {
  try {
    console.warn('⚠️ Using deprecated readAllInvoices from invoices-repo. Consider using getAllInvoices directly.');
    return await getAllInvoices();
  } catch (error) {
    console.error('Error reading invoices from storage:', error);
    throw error;
  }
}

/**
 * Get invoice by invoice number
 * @deprecated Use getInvoiceByNumber from invoice-storage directly
 */
export async function getInvoiceByNumber(invoiceNumber: string): Promise<InvoiceRecord | null> {
  try {
    console.warn('⚠️ Using deprecated getInvoiceByNumber from invoices-repo. Consider using getInvoiceByNumber directly.');
    return await getInvoiceByNumberFromStorage(invoiceNumber);
  } catch (error) {
    console.error(`Error reading invoice ${invoiceNumber}:`, error);
    return null;
  }
}

/**
 * Get invoices by project ID
 * @deprecated Use getAllInvoices and filter directly
 */
export async function listInvoicesByProject(projectId: number): Promise<InvoiceRecord[]> {
  try {
    console.warn('⚠️ Using deprecated listInvoicesByProject from invoices-repo. Consider using getAllInvoices and filtering directly.');
    const allInvoices = await getAllInvoices();
    return allInvoices.filter(invoice => invoice.projectId === projectId);
  } catch (error) {
    console.error(`Error reading invoices for project ${projectId}:`, error);
    throw error;
  }
}

/**
 * Get invoices by freelancer ID
 * @deprecated Use getAllInvoices and filter directly
 */
export async function listInvoicesByFreelancer(freelancerId: number): Promise<InvoiceRecord[]> {
  try {
    console.warn('⚠️ Using deprecated listInvoicesByFreelancer from invoices-repo. Consider using getAllInvoices and filtering directly.');
    const allInvoices = await getAllInvoices();
    return allInvoices.filter(invoice => invoice.freelancerId === freelancerId);
  } catch (error) {
    console.error(`Error reading invoices for freelancer ${freelancerId}:`, error);
    throw error;
  }
}

/**
 * Get invoices by commissioner ID
 * @deprecated Use getAllInvoices and filter directly
 */
export async function listInvoicesByCommissioner(commissionerId: number): Promise<InvoiceRecord[]> {
  try {
    console.warn('⚠️ Using deprecated listInvoicesByCommissioner from invoices-repo. Consider using getAllInvoices and filtering directly.');
    const allInvoices = await getAllInvoices();
    return allInvoices.filter(invoice => invoice.commissionerId === commissionerId);
  } catch (error) {
    console.error(`Error reading invoices for commissioner ${commissionerId}:`, error);
    throw error;
  }
}

/**
 * Create or update an invoice
 * @deprecated Use saveInvoice from invoice-storage directly
 */
export async function saveInvoiceRecord(invoice: InvoiceRecord): Promise<InvoiceRecord> {
  try {
    console.warn('⚠️ Using deprecated saveInvoiceRecord from invoices-repo. Consider using saveInvoice directly.');
    
    const invoiceToSave = {
      ...invoice,
      updatedAt: new Date().toISOString()
    };
    
    await saveInvoice(invoiceToSave);
    return invoiceToSave;
  } catch (error) {
    console.error('Error saving invoice:', error);
    throw error;
  }
}

/**
 * Update an existing invoice
 * @deprecated Use saveInvoice from invoice-storage directly
 */
export async function updateInvoice(invoiceNumber: string, updates: Partial<InvoiceRecord>): Promise<InvoiceRecord | null> {
  try {
    console.warn('⚠️ Using deprecated updateInvoice from invoices-repo. Consider using saveInvoice directly.');
    
    const existingInvoice = await getInvoiceByNumber(invoiceNumber);
    if (!existingInvoice) {
      return null;
    }
    
    const updatedInvoice = {
      ...existingInvoice,
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    await saveInvoice(updatedInvoice);
    return updatedInvoice;
  } catch (error) {
    console.error(`Error updating invoice ${invoiceNumber}:`, error);
    throw error;
  }
}

/**
 * Create a new invoice
 * @deprecated Use saveInvoice from invoice-storage directly
 */
export async function createInvoice(invoice: Omit<InvoiceRecord, 'createdAt' | 'updatedAt'>): Promise<InvoiceRecord> {
  try {
    console.warn('⚠️ Using deprecated createInvoice from invoices-repo. Consider using saveInvoice directly.');
    
    const now = new Date().toISOString();
    const newInvoice = {
      ...invoice,
      createdAt: now,
      updatedAt: now
    };
    
    await saveInvoice(newInvoice);
    return newInvoice;
  } catch (error) {
    console.error('Error creating invoice:', error);
    throw error;
  }
}

/**
 * Delete an invoice
 * @deprecated Not implemented in compatibility layer
 */
export async function deleteInvoice(invoiceNumber: string): Promise<boolean> {
  try {
    console.warn('⚠️ Using deprecated deleteInvoice from invoices-repo. Invoice deletion not implemented in compatibility layer.');
    console.warn('⚠️ Use invoice storage utilities directly for deletion operations.');
    return false;
  } catch (error) {
    console.error(`Error deleting invoice ${invoiceNumber}:`, error);
    return false;
  }
}
